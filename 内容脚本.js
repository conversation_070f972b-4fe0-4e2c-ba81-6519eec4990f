// 闲鱼采集插件 - 内容脚本
(function() {
    'use strict';
    
    // 检查是否已经添加了按钮，避免重复添加
    if (document.getElementById('goofish-collection-button')) {
        return;
    }
    
    // 创建采集按钮
    function createCollectionButton() {
        const button = document.createElement('button');
        button.id = 'goofish-collection-button';
        button.className = 'collection-button';
        button.textContent = '采集页面';
        button.title = '点击采集当前页面数据';
        
        // 添加点击事件 - 采集商品内容功能
        button.addEventListener('click', function() {
            console.log('采集按钮被点击');
            collectProductContent();
        });
        
        return button;
    }
    
    // 添加按钮到页面
    function addButtonToPage() {
        const button = createCollectionButton();
        document.body.appendChild(button);
        console.log('闲鱼采集按钮已添加到页面');
    }
    
    // 等待页面加载完成
    function initializePlugin() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', addButtonToPage);
        } else {
            addButtonToPage();
        }
    }
    
    // 检查当前URL是否为闲鱼网站
    function isGoofishSite() {
        return window.location.hostname === 'www.goofish.com';
    }
    
    // 监听页面URL变化（SPA应用）
    function observeUrlChanges() {
        let currentUrl = window.location.href;
        
        const observer = new MutationObserver(function() {
            if (currentUrl !== window.location.href) {
                currentUrl = window.location.href;
                
                // URL变化时重新检查是否需要显示按钮
                const existingButton = document.getElementById('goofish-collection-button');
                if (isGoofishSite() && !existingButton) {
                    setTimeout(addButtonToPage, 1000); // 延迟添加，确保页面加载完成
                }
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    // 商品内容采集功能
    function collectProductContent() {
        // 获取商家名字
        const merchantName = getMerchantName();

        // 生成时间戳
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_');

        // 生成文件名：商家名字_时间戳
        const filename = `${merchantName}_${timestamp}`;

        // 采集商品描述内容
        const productDescription = getProductDescription();

        // 采集图片URL
        const imageUrls = getImageUrls();

        // 采集其他商品信息
        const productInfo = getProductInfo();

        // 创建完整的商品数据
        const productData = {
            merchantName: merchantName,
            collectTime: new Date().toISOString(),
            pageUrl: window.location.href,
            pageTitle: document.title,
            productDescription: productDescription,
            productInfo: productInfo,
            totalImages: imageUrls.length,
            images: imageUrls
        };

        // 直接下载JSON文件，无任何提示
        downloadJsonFile(productData, filename);
        console.log(`商品内容已采集：${filename}.json，包含商品描述和 ${imageUrls.length} 张图片地址`);
    }

    // 获取商品描述内容
    function getProductDescription() {
        // 尝试多种可能的选择器来获取商品描述
        const selectors = [
            '.notLoginContainer--hQCDYhxp .main--Nu33bWl6 .desc--GaIUKUQY',
            '.desc--GaIUKUQY',
            '[class*="desc--"]',
            '[class*="description"]',
            '[class*="content"]'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                // 获取纯文本内容，去除HTML标签
                const textContent = element.innerText || element.textContent || '';
                if (textContent.trim()) {
                    return textContent.trim();
                }
            }
        }

        return '未找到商品描述';
    }

    // 获取图片URL列表
    function getImageUrls() {
        const imageContainers = document.querySelectorAll('.slick-slide img, .carouselItem--jwFj0Jpa img');
        const imageUrls = [];

        imageContainers.forEach((img, index) => {
            const imgSrc = img.src || img.getAttribute('data-src');
            if (imgSrc) {
                // 处理图片URL，移除可能的尺寸参数获取原图
                const cleanUrl = cleanImageUrl(imgSrc);
                imageUrls.push({
                    index: index + 1,
                    url: cleanUrl,
                    originalUrl: imgSrc
                });
            }
        });

        return imageUrls;
    }

    // 获取其他商品信息
    function getProductInfo() {
        const productInfo = {};

        // 获取预计工期
        const workPeriodElement = document.querySelector('.labels--ndhPFgp8 .item--qI9ENIfp .value--EyQBSInp');
        if (workPeriodElement) {
            productInfo.workPeriod = workPeriodElement.textContent.trim();
        }

        // 获取计价方式
        const pricingElements = document.querySelectorAll('.labels--ndhPFgp8 .item--qI9ENIfp .value--EyQBSInp');
        if (pricingElements.length > 1) {
            productInfo.pricingMethod = pricingElements[1].textContent.trim();
        }

        // 获取商品标题
        const titleElement = document.querySelector('h1, .title, [class*="title"]');
        if (titleElement) {
            productInfo.title = titleElement.textContent.trim();
        }

        return productInfo;
    }

    // 获取商家名字
    function getMerchantName() {
        // 尝试多种可能的选择器来获取商家名字
        const selectors = [
            '.item-user-info-nick--rtpDhkmQ',
            '[class*="item-user-info-nick"]',
            '[class*="user-info-nick"]',
            '[class*="seller-nick"]',
            '[class*="merchant-name"]'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
                return element.textContent.trim().replace(/[^\w\u4e00-\u9fa5]/g, '_');
            }
        }

        // 如果找不到商家名字，使用页面标题或默认值
        const pageTitle = document.title.replace(/[^\w\u4e00-\u9fa5]/g, '_').slice(0, 20);
        return pageTitle || '闲鱼商品';
    }

    // 清理图片URL，移除尺寸参数获取原图
    function cleanImageUrl(url) {
        // 移除阿里云图片处理参数，获取原图
        return url.replace(/_\d+x\d+Q\d+\.jpg_\.webp$/, '')
                 .replace(/_\d+x\d+Q\d+\.jpg_$/, '.jpg')
                 .replace(/\.webp$/, '.jpg');
    }

    // 下载JSON文件
    function downloadJsonFile(data, filename) {
        try {
            const jsonString = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${filename}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);
        } catch (error) {
            console.error('下载JSON文件失败:', error);
        }
    }



    // 初始化插件
    if (isGoofishSite()) {
        initializePlugin();
        observeUrlChanges();
    }

})();
