// 闲鱼采集插件 - 内容脚本
(function() {
    'use strict';
    
    // 检查是否已经添加了按钮，避免重复添加
    if (document.getElementById('goofish-collection-button')) {
        return;
    }
    
    // 创建采集按钮
    function createCollectionButton() {
        const button = document.createElement('button');
        button.id = 'goofish-collection-button';
        button.className = 'collection-button';
        button.textContent = '采集页面';
        button.title = '点击采集当前页面数据';
        
        // 添加点击事件 - 采集图片功能
        button.addEventListener('click', function() {
            console.log('采集按钮被点击');
            collectImages();
        });
        
        return button;
    }
    
    // 添加按钮到页面
    function addButtonToPage() {
        const button = createCollectionButton();
        document.body.appendChild(button);
        console.log('闲鱼采集按钮已添加到页面');
    }
    
    // 等待页面加载完成
    function initializePlugin() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', addButtonToPage);
        } else {
            addButtonToPage();
        }
    }
    
    // 检查当前URL是否为闲鱼网站
    function isGoofishSite() {
        return window.location.hostname === 'www.goofish.com';
    }
    
    // 监听页面URL变化（SPA应用）
    function observeUrlChanges() {
        let currentUrl = window.location.href;
        
        const observer = new MutationObserver(function() {
            if (currentUrl !== window.location.href) {
                currentUrl = window.location.href;
                
                // URL变化时重新检查是否需要显示按钮
                const existingButton = document.getElementById('goofish-collection-button');
                if (isGoofishSite() && !existingButton) {
                    setTimeout(addButtonToPage, 1000); // 延迟添加，确保页面加载完成
                }
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    // 图片采集功能
    function collectImages() {
        // 查找图片容器
        const imageContainers = document.querySelectorAll('.slick-slide img, .carouselItem--jwFj0Jpa img');

        if (imageContainers.length === 0) {
            alert('未找到图片，请确保在商品详情页面');
            return;
        }

        console.log(`找到 ${imageContainers.length} 张图片`);

        // 生成唯一的文件夹标识
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_');
        const pageTitle = document.title.replace(/[^\w\u4e00-\u9fa5]/g, '_').slice(0, 20) || '闲鱼商品';
        const folderPrefix = `${pageTitle}_${timestamp}`;

        // 显示进度提示
        showProgressMessage(`开始下载 ${imageContainers.length} 张图片...`);

        let downloadCount = 0;
        const totalImages = imageContainers.length;

        // 遍历所有图片并下载
        imageContainers.forEach((img, index) => {
            const imgSrc = img.src || img.getAttribute('data-src');
            if (imgSrc) {
                // 处理图片URL，移除可能的尺寸参数
                const cleanUrl = cleanImageUrl(imgSrc);
                const filename = `${folderPrefix}_图片${String(index + 1).padStart(2, '0')}`;

                downloadImage(cleanUrl, filename, () => {
                    downloadCount++;
                    updateProgressMessage(`已下载 ${downloadCount}/${totalImages} 张图片`);

                    if (downloadCount === totalImages) {
                        setTimeout(() => {
                            hideProgressMessage();
                            alert(`图片采集完成！共下载 ${totalImages} 张图片\n文件名格式：${folderPrefix}_图片XX.jpg\n建议手动创建文件夹整理`);
                        }, 1000);
                    }
                });
            }
        });
    }

    // 清理图片URL，移除尺寸参数获取原图
    function cleanImageUrl(url) {
        // 移除阿里云图片处理参数，获取原图
        return url.replace(/_\d+x\d+Q\d+\.jpg_\.webp$/, '')
                 .replace(/_\d+x\d+Q\d+\.jpg_$/, '.jpg')
                 .replace(/\.webp$/, '.jpg');
    }

    // 下载单张图片
    function downloadImage(url, filename, callback) {
        fetch(url)
            .then(response => response.blob())
            .then(blob => {
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `${filename}.jpg`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(link.href);

                if (callback) callback();
            })
            .catch(error => {
                console.error('下载图片失败:', error);
                if (callback) callback();
            });
    }

    // 显示进度消息
    function showProgressMessage(message) {
        let progressDiv = document.getElementById('goofish-progress-message');
        if (!progressDiv) {
            progressDiv = document.createElement('div');
            progressDiv.id = 'goofish-progress-message';
            progressDiv.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                z-index: 10000;
                font-size: 14px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            `;
            document.body.appendChild(progressDiv);
        }
        progressDiv.textContent = message;
        progressDiv.style.display = 'block';
    }

    // 更新进度消息
    function updateProgressMessage(message) {
        const progressDiv = document.getElementById('goofish-progress-message');
        if (progressDiv) {
            progressDiv.textContent = message;
        }
    }

    // 隐藏进度消息
    function hideProgressMessage() {
        const progressDiv = document.getElementById('goofish-progress-message');
        if (progressDiv) {
            progressDiv.style.display = 'none';
        }
    }

    // 初始化插件
    if (isGoofishSite()) {
        initializePlugin();
        observeUrlChanges();
    }

})();
